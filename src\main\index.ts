// import 'reflect-metadata'; // Note: Required by TypeORM for decorator metadata, kept commented for easy restoration if needed
import { app, BrowserWindow, session, OnHeadersReceivedListenerDetails, HeadersReceivedResponse, Menu, ipcMain, dialog, autoUpdater } from 'electron';
import * as path from 'path';
import { store } from './store';
import { setupDatabase } from './database/connection';
import { setupAuthHandlers } from './ipc/auth';
// Import database handlers
import './ipc/database';
import { CustomerPackageService } from './database/services/CustomerPackageService';

declare const MAIN_WINDOW_WEBPACK_ENTRY: string;
declare const MAIN_WINDOW_PRELOAD_WEBPACK_ENTRY: string;

// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (require('electron-squirrel-startup')) {
  app.quit();
}

// Setup IPC handlers for electron-store
ipcMain.handle('electron-store-get', async (_, key) => {
  return store.get(key);
});

ipcMain.handle('electron-store-set', async (_, key, val) => {
  store.set(key, val);
  return true;
});

let mainWindow: BrowserWindow | null = null;
let splashWindow: BrowserWindow | null = null;
let isAppReady = false;
let isMainWindowReady = false;

// Create the splash screen window
const createSplashWindow = (): BrowserWindow => {
  // Get the correct path to the loading.html file
  let loadingHtmlPath: string;

  if (process.env.NODE_ENV === 'development') {
    // Development path
    loadingHtmlPath = path.join(app.getAppPath(), 'public', 'loading.html');
    console.log('Development loading path:', loadingHtmlPath);
  } else {
    // In packaged builds, the correct path is resources/public/loading.html
    loadingHtmlPath = path.join(process.resourcesPath, 'public', 'loading.html');
    console.log('Production loading path:', loadingHtmlPath);

    // Fallback paths if the primary path doesn't work
    const fs = require('fs');
    if (!fs.existsSync(loadingHtmlPath)) {
      console.log(`Primary loading path doesn't exist, trying fallbacks`);

      const fallbackPaths = [
        path.join(process.resourcesPath, 'app', 'public', 'loading.html'),
        path.join(app.getAppPath(), 'public', 'loading.html'),
        path.join(app.getAppPath(), 'resources', 'public', 'loading.html'),
        path.join(__dirname, '..', 'public', 'loading.html'),
        path.join(__dirname, '..', '..', 'public', 'loading.html')
      ];

      for (const p of fallbackPaths) {
        if (fs.existsSync(p)) {
          console.log(`Found loading.html at fallback path: ${p}`);
          loadingHtmlPath = p;
          break;
        }
      }
    }
  }

  console.log('Loading splash screen from:', loadingHtmlPath);

  const window = new BrowserWindow({
    width: 400,
    height: 400,
    transparent: false,
    frame: false,
    resizable: false,
    movable: false,
    center: true,
    alwaysOnTop: true,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'splashPreload.js'),
    },
  });

  // Load the splash screen HTML
  window.loadFile(loadingHtmlPath).catch(err => {
    console.error('Failed to load splash screen with loadFile:', err);
    // Fallback: try using loadURL with file:// protocol
    const fileUrl = `file://${loadingHtmlPath}`;
    console.log('Trying fallback with loadURL:', fileUrl);
    window.loadURL(fileUrl).catch(urlErr => {
      console.error('Also failed with loadURL:', urlErr);

      // Last resort: create a minimal HTML content directly
      const minimalHtml = `
        <html>
          <head>
            <style>
              body {
                font-family: Arial, sans-serif;
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100vh;
                margin: 0;
                background-color: white;
                color: #333;
              }
              .loader {
                border: 5px solid #f3f3f3;
                border-top: 5px solid #3498db;
                border-radius: 50%;
                width: 50px;
                height: 50px;
                animation: spin 2s linear infinite;
                margin-bottom: 20px;
              }
              @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
              }
              .container {
                text-align: center;
              }
            </style>
          </head>
          <body>
            <div class="container">
              <div class="loader"></div>
              <p>Carregando...</p>
            </div>
          </body>
        </html>
      `;
      window.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(minimalHtml)}`);
    });
  });

  // For debugging
  window.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    console.error('Failed to load splash screen:', errorCode, errorDescription);
  });

  // Only show the window once it's fully loaded to prevent flashing
  window.once('ready-to-show', () => {
    window.show();
  });

  return window;
};

const createWindow = (): BrowserWindow => {
  // Create the browser window.
  const appVersion = app.getVersion();
  const windowTitle = `Space Pet - v${appVersion}`;

  const window = new BrowserWindow({
    width: 1200,
    height: 800,
    title: windowTitle,
    icon: path.join(app.getAppPath(), 'src', 'assets', 'pao.png'),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: MAIN_WINDOW_PRELOAD_WEBPACK_ENTRY,
      devTools: process.env.NODE_ENV === 'development', // Only enable DevTools in development
    },
    // Make the app look more native
    frame: true, // Keep the frame for window controls (min/max/close)
    autoHideMenuBar: true, // Hide menu bar but allow pressing Alt to show it
    backgroundColor: '#ffffff', // Set background color to prevent white flash
    show: false, // Hide until fully loaded
  });

  // Remove menu bar completely
  Menu.setApplicationMenu(null);

  // Configure CSP
  session.defaultSession.webRequest.onHeadersReceived(
    (details: OnHeadersReceivedListenerDetails, callback: (response: HeadersReceivedResponse) => void) => {
      callback({
        responseHeaders: {
          ...details.responseHeaders,
          'Content-Security-Policy': [
            "default-src 'self' 'unsafe-inline' 'unsafe-eval' ws://localhost:* http://localhost:*; img-src 'self' data: blob:"
          ]
        }
      });
    }
  );

  // and load the index.html of the app.
  if (MAIN_WINDOW_WEBPACK_ENTRY) {
    window.loadURL(MAIN_WINDOW_WEBPACK_ENTRY);
  }

  // Listen for when the window is ready to be shown
  window.webContents.on('did-finish-load', () => {
    isMainWindowReady = true;
    showMainWindowWhenReady();
  });

  // Open the DevTools in development
  if (process.env.NODE_ENV === 'development') {
    window.webContents.openDevTools();
  } else {
    // Prevent DevTools from being opened in production
    window.webContents.on('devtools-opened', () => {
      window.webContents.closeDevTools();
    });
  }

  return window;
};

// Show the main window and close splash when the app is ready
const showMainWindowWhenReady = () => {
  if (isAppReady && isMainWindowReady && mainWindow && splashWindow) {
    // Slight delay to ensure smooth transition
    setTimeout(() => {
      if (mainWindow) {
        mainWindow.show();

        // Center the main window based on current screen
        mainWindow.center();

        // Bring to front on Windows
        if (process.platform === 'win32') {
          mainWindow.setAlwaysOnTop(true);
          mainWindow.focus();
          mainWindow.setAlwaysOnTop(false);
        }
      }

      // Close splash window after a short delay to avoid flash
      setTimeout(() => {
        if (splashWindow) {
          splashWindow.destroy();
          splashWindow = null;
        }
      }, 500);
    }, 800);
  }
};

// Listen for IPC from renderer when UI is ready
ipcMain.on('splash-ready', () => {
  console.log('Splash screen is ready');
});

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.on('ready', async () => {
  try {
    // Configure autoUpdater
    const server = 'https://update.electronjs.org'
    const feed = `${server}/unknown7987/spacepet-releases/${process.platform}-${process.arch}/${app.getVersion()}`

    autoUpdater.setFeedURL({ url: feed });

    // Show splash screen immediately
    splashWindow = createSplashWindow();

    // Create main window but keep it hidden
    mainWindow = createWindow();

    // Setup IPC handler to notify frontend when database is ready
    ipcMain.handle('get-database-status', () => ({ isReady: false }));

    // Initialize database in the background
    await setupDatabase();
    setupAuthHandlers();

    // Run payment status migration for existing packages
    try {
      const customerPackageService = new CustomerPackageService();
      await customerPackageService.migratePaymentStatusFields();
    } catch (error) {
      console.error('Failed to run payment status migration:', error);
    }

    // Notify renderer that database is ready
    ipcMain.removeHandler('get-database-status');
    ipcMain.handle('get-database-status', () => ({ isReady: true }));

    // Send event to renderer to indicate database is ready
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('database-ready');
    }

    // Set app as ready
    isAppReady = true;

    // Check if we can show the main window
    showMainWindowWhenReady();

    // Check for updates after a short delay to ensure the feed URL is set and app is somewhat settled.
    // And only in production environments
    if (process.env.NODE_ENV !== 'development') {
      setTimeout(() => {
        console.log('Checking for updates with feed URL:', feed);
        autoUpdater.checkForUpdates(); // Initiate update check (built-in)
      }, 30000); // 30-second delay
    }
  } catch (error) {
    console.error('Failed to initialize the application:', error);
    app.quit();
  }
});

// Listen for main-window-ready event from the React app
ipcMain.on('main-window-ready', () => {
  if (mainWindow && splashWindow) {
    isMainWindowReady = true;
    showMainWindowWhenReady();
  }
});

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('will-quit', () => {
  // App cleanup logic can be added here if needed
});

app.on('activate', () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    mainWindow = createWindow();
  }
});

// Configurando listeners para o built-in autoUpdater
autoUpdater.on('error', (err) => {
  console.error('Erro no auto-updater:', err);
  // Add a dialog to inform the user about the update error
  //dialog.showErrorBox('Erro na Atualização Automática', `Ocorreu um erro ao procurar ou baixar atualizações: ${err.message}`);
});
autoUpdater.on('checking-for-update', () => {
  console.log('Procurando atualizações...');
});
autoUpdater.on('update-available', () => {
  console.log('Atualização disponível. Fazendo download...');
});
autoUpdater.on('update-not-available', () => {
  console.log('Nenhuma atualização disponível.');
});
autoUpdater.on('update-downloaded', () => {
  dialog.showMessageBox({
    type: 'info',
    buttons: ['Sim', 'Não'],
    title: 'Atualização Disponível',
    message: 'Uma nova versão foi baixada. Deseja reiniciar agora para instalar a atualização?',
  }).then((result) => {
    if (result.response === 0) {
      autoUpdater.quitAndInstall();
    }
  });
});

// Window focus event listener removed - no automatic no-show detection
// Manual IPC handler removed - admins will handle no-shows manually through UI